using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Controls individual bubble movement and animation
/// Handles upward movement, horizontal drift, sine wave motion, and fade effects
/// </summary>
public class BubbleController : MonoBehaviour
{
    // Movement properties
    private float moveSpeed;
    private float horizontalDrift;
    private float sineAmplitude;
    private float sineFrequency;
    private bool shouldFadeOut;
    private float fadeDistance;
    private float tankHeight;
    
    // Runtime variables
    private RectTransform rectTransform;
    private Image bubbleImage;
    private Vector2 startPosition;
    private float timeAlive;
    private float originalAlpha;
    private Vector2 driftDirection;
    
    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        bubbleImage = GetComponent<Image>();
    }
    
    /// <summary>
    /// Initialize bubble with movement parameters
    /// </summary>
    public void Initialize(float speed, float drift, float amplitude, float frequency, 
                          bool fadeOut, float fadeOutDistance, float tankHeightValue)
    {
        moveSpeed = speed;
        horizontalDrift = drift;
        sineAmplitude = amplitude;
        sineFrequency = frequency;
        shouldFadeOut = fadeOut;
        fadeDistance = fadeOutDistance;
        tankHeight = tankHeightValue;
        
        // Reset runtime variables
        timeAlive = 0f;
        startPosition = rectTransform.anchoredPosition;
        
        // Store original alpha for fade calculations
        if (bubbleImage != null)
        {
            originalAlpha = bubbleImage.color.a;
        }
        
        // Set random drift direction
        driftDirection = new Vector2(
            Random.Range(-1f, 1f),
            Random.Range(0.5f, 1f)
        ).normalized;
    }
    
    private void Update()
    {
        MoveBubble();
        HandleFadeEffect();
        timeAlive += Time.deltaTime;
    }
    
    /// <summary>
    /// Handle bubble movement with upward motion, drift, and sine wave
    /// </summary>
    private void MoveBubble()
    {
        if (rectTransform == null) return;
        
        Vector2 currentPosition = rectTransform.anchoredPosition;
        
        // Base upward movement
        float upwardMovement = moveSpeed * Time.deltaTime;
        
        // Horizontal drift with some randomness
        float horizontalMovement = horizontalDrift * driftDirection.x * Time.deltaTime;
        
        // Add sine wave motion for more organic movement
        float sineOffset = Mathf.Sin(timeAlive * sineFrequency) * sineAmplitude * Time.deltaTime;
        
        // Apply movement
        Vector2 newPosition = new Vector2(
            currentPosition.x + horizontalMovement + sineOffset,
            currentPosition.y + upwardMovement
        );
        
        rectTransform.anchoredPosition = newPosition;
    }
    
    /// <summary>
    /// Handle fade out effect as bubble approaches the top
    /// </summary>
    private void HandleFadeEffect()
    {
        if (!shouldFadeOut || bubbleImage == null) return;
        
        float currentY = rectTransform.anchoredPosition.y;
        float topOfTank = tankHeight * 0.5f;
        float fadeStartY = topOfTank - fadeDistance;
        
        if (currentY >= fadeStartY)
        {
            // Calculate fade factor (0 = fully visible, 1 = fully transparent)
            float fadeProgress = (currentY - fadeStartY) / fadeDistance;
            fadeProgress = Mathf.Clamp01(fadeProgress);
            
            // Apply fade
            Color color = bubbleImage.color;
            color.a = originalAlpha * (1f - fadeProgress);
            bubbleImage.color = color;
        }
        else
        {
            // Ensure full alpha when not in fade zone
            Color color = bubbleImage.color;
            color.a = originalAlpha;
            bubbleImage.color = color;
        }
    }
    
    /// <summary>
    /// Reset bubble to initial state (called when returned to pool)
    /// </summary>
    public void ResetBubble()
    {
        timeAlive = 0f;
        
        if (bubbleImage != null)
        {
            Color color = bubbleImage.color;
            color.a = originalAlpha;
            bubbleImage.color = color;
        }
    }
    
    private void OnDisable()
    {
        ResetBubble();
    }
}
